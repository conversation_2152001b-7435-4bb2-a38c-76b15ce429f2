#!/usr/bin/env node
/**
 * Test Runner for PersistentWhisperEngine Performance Validation
 * 
 * This script runs comprehensive tests to validate that the new implementation meets all targets:
 * 1. Model loads only once
 * 2. Per-chunk transcription latency is under 500ms
 * 3. Interim results are delivered smoothly
 * 4. VAD integration works correctly
 * 5. Fallback to batch processing still works
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 PersistentWhisperEngine Performance Test Runner');
console.log('=' .repeat(60));

// Check if the project is built
const distPath = path.join(__dirname, 'dist');
if (!fs.existsSync(distPath)) {
  console.log('❌ Project not built. Please run "npm run build" first.');
  process.exit(1);
}

console.log('✅ Project build found');

// List of tests to run
const tests = [
  {
    name: 'PersistentWhisperEngine Performance Test',
    script: 'test-persistent-whisper-performance.js',
    description: 'Validates the new persistent engine implementation'
  },
  {
    name: 'Legacy Performance Comparison',
    script: 'test-performance.js',
    description: 'Compares with legacy implementation (if available)'
  }
];

async function runTest(test) {
  console.log(`\n🧪 Running: ${test.name}`);
  console.log(`📝 Description: ${test.description}`);
  console.log('-' .repeat(40));

  const testPath = path.join(__dirname, test.script);
  
  if (!fs.existsSync(testPath)) {
    console.log(`⚠️  Test script not found: ${test.script}`);
    return false;
  }

  return new Promise((resolve) => {
    const child = spawn('node', [testPath], {
      stdio: 'inherit',
      cwd: __dirname
    });

    child.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ ${test.name} PASSED`);
        resolve(true);
      } else {
        console.log(`❌ ${test.name} FAILED (exit code: ${code})`);
        resolve(false);
      }
    });

    child.on('error', (error) => {
      console.log(`💥 ${test.name} ERROR: ${error.message}`);
      resolve(false);
    });
  });
}

async function runAllTests() {
  console.log(`\n📋 Running ${tests.length} test suite(s)...\n`);

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    const success = await runTest(test);
    if (success) {
      passed++;
    } else {
      failed++;
    }
  }

  console.log('\n📊 Test Results Summary');
  console.log('=' .repeat(60));
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Total: ${passed + failed}`);

  if (failed === 0) {
    console.log('\n🎉 ALL TESTS PASSED!');
    console.log('🚀 PersistentWhisperEngine is ready for production use.');
    console.log('\n📈 Performance improvements achieved:');
    console.log('   • Model loading overhead eliminated');
    console.log('   • Transcription latency reduced to <500ms');
    console.log('   • Real-time interim results delivered');
    console.log('   • VAD integration optimized');
    console.log('   • Fallback system validated');
    process.exit(0);
  } else {
    console.log('\n⚠️  Some tests failed. Please review the output above.');
    console.log('💡 Common issues and solutions:');
    console.log('   • Model files missing: Run model download script');
    console.log('   • Build issues: Run "npm run build"');
    console.log('   • Dependencies: Run "npm install"');
    console.log('   • Permissions: Check file system permissions');
    process.exit(1);
  }
}

// Check for command line arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  console.log('\nUsage: node run-performance-tests.js [options]');
  console.log('\nOptions:');
  console.log('  --help, -h     Show this help message');
  console.log('  --verbose, -v  Enable verbose output');
  console.log('\nThis script validates the PersistentWhisperEngine implementation');
  console.log('against all performance targets and requirements.');
  process.exit(0);
}

if (args.includes('--verbose') || args.includes('-v')) {
  console.log('\n🔍 Verbose mode enabled');
  console.log('📁 Working directory:', __dirname);
  console.log('📁 Dist directory:', distPath);
  console.log('📋 Tests to run:', tests.map(t => t.name).join(', '));
}

// Run the tests
runAllTests().catch(error => {
  console.error('\n💥 Test runner failed:', error);
  process.exit(1);
});
