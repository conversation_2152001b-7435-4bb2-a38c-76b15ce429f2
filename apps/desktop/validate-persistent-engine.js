#!/usr/bin/env node
/**
 * Quick Validation Script for PersistentWhisperEngine
 * 
 * This script performs basic validation to ensure the PersistentWhisperEngine
 * is properly integrated and functioning correctly.
 */

const { performance } = require('perf_hooks');
const path = require('path');
const fs = require('fs');

async function validatePersistentEngine() {
  console.log('🔍 PersistentWhisperEngine Quick Validation');
  console.log('=' .repeat(50));

  let validationsPassed = 0;
  let totalValidations = 0;

  try {
    // Validation 1: Check if build exists
    totalValidations++;
    console.log('\n📋 Validation 1: Build Check');
    const distPath = path.join(__dirname, 'dist');
    if (fs.existsSync(distPath)) {
      console.log('   ✅ Build directory found');
      validationsPassed++;
    } else {
      console.log('   ❌ Build directory not found - run "npm run build"');
      return false;
    }

    // Validation 2: Import PersistentWhisperEngine
    totalValidations++;
    console.log('\n📋 Validation 2: PersistentWhisperEngine Import');
    try {
      const PersistentWhisperEngine = require('./dist/electron/helpers/PersistentWhisperEngine').PersistentWhisperEngine;
      if (PersistentWhisperEngine) {
        console.log('   ✅ PersistentWhisperEngine class imported successfully');
        validationsPassed++;
      } else {
        console.log('   ❌ PersistentWhisperEngine class not found in exports');
      }
    } catch (error) {
      console.log(`   ❌ Failed to import PersistentWhisperEngine: ${error.message}`);
    }

    // Validation 3: Import StreamingAudioProcessor
    totalValidations++;
    console.log('\n📋 Validation 3: StreamingAudioProcessor Import');
    try {
      const StreamingAudioProcessor = require('./dist/electron/helpers/StreamingAudioProcessor').default;
      if (StreamingAudioProcessor) {
        console.log('   ✅ StreamingAudioProcessor imported successfully');
        validationsPassed++;
      } else {
        console.log('   ❌ StreamingAudioProcessor not found in exports');
      }
    } catch (error) {
      console.log(`   ❌ Failed to import StreamingAudioProcessor: ${error.message}`);
    }

    // Validation 4: Check model files
    totalValidations++;
    console.log('\n📋 Validation 4: Model Files Check');
    try {
      const projectRoot = path.resolve(__dirname, '../..');
      const whisperCppDir = path.join(projectRoot, 'node_modules', 'nodejs-whisper', 'cpp', 'whisper.cpp');
      const tinyModelPath = path.join(whisperCppDir, 'models', 'ggml-tiny.en.bin');
      const baseModelPath = path.join(whisperCppDir, 'models', 'ggml-base.en.bin');
      
      if (fs.existsSync(tinyModelPath)) {
        console.log('   ✅ tiny.en model found');
        validationsPassed++;
      } else {
        console.log('   ❌ tiny.en model not found');
        console.log(`   📁 Expected at: ${tinyModelPath}`);
      }
      
      if (fs.existsSync(baseModelPath)) {
        console.log('   ✅ base.en model found (for fallback)');
      } else {
        console.log('   ⚠️  base.en model not found (fallback may not work)');
      }
    } catch (error) {
      console.log(`   ❌ Error checking model files: ${error.message}`);
    }

    // Validation 5: Basic instantiation test
    totalValidations++;
    console.log('\n📋 Validation 5: Basic Instantiation Test');
    try {
      const StreamingAudioProcessor = require('./dist/electron/helpers/StreamingAudioProcessor').default;
      const processor = new StreamingAudioProcessor({
        modelSize: 'tiny.en',
        enableContinuousStreaming: true
      });
      
      if (processor) {
        console.log('   ✅ StreamingAudioProcessor instantiated successfully');
        
        // Check if it has the new methods
        if (typeof processor.getEngineStatus === 'function') {
          console.log('   ✅ New engine status method available');
        } else {
          console.log('   ❌ Engine status method not found');
        }
        
        if (typeof processor.optimizeForPersistentEngine === 'function') {
          console.log('   ✅ Optimization method available');
        } else {
          console.log('   ❌ Optimization method not found');
        }
        
        validationsPassed++;
      } else {
        console.log('   ❌ Failed to instantiate StreamingAudioProcessor');
      }
    } catch (error) {
      console.log(`   ❌ Instantiation failed: ${error.message}`);
    }

    // Validation 6: Configuration validation
    totalValidations++;
    console.log('\n📋 Validation 6: Configuration Validation');
    try {
      const StreamingAudioProcessor = require('./dist/electron/helpers/StreamingAudioProcessor').default;
      const processor = new StreamingAudioProcessor();
      
      // Check if optimized settings are applied
      const status = processor.getStatus();
      if (status) {
        console.log('   ✅ Processor status accessible');
        console.log(`   📊 Current state: ${JSON.stringify(status)}`);
        validationsPassed++;
      } else {
        console.log('   ❌ Could not get processor status');
      }
    } catch (error) {
      console.log(`   ❌ Configuration validation failed: ${error.message}`);
    }

    // Summary
    console.log('\n📊 Validation Summary');
    console.log('=' .repeat(50));
    console.log(`✅ Passed: ${validationsPassed}/${totalValidations}`);
    console.log(`📊 Success Rate: ${Math.round(validationsPassed / totalValidations * 100)}%`);

    if (validationsPassed === totalValidations) {
      console.log('\n🎉 ALL VALIDATIONS PASSED!');
      console.log('✅ PersistentWhisperEngine is properly integrated');
      console.log('🚀 Ready for performance testing');
      console.log('\nNext steps:');
      console.log('   1. Run: node run-performance-tests.js');
      console.log('   2. Test in the actual application');
      console.log('   3. Monitor performance metrics');
      return true;
    } else if (validationsPassed >= totalValidations * 0.8) {
      console.log('\n⚠️  MOST VALIDATIONS PASSED');
      console.log('✅ Core functionality appears to be working');
      console.log('⚠️  Some optional features may need attention');
      console.log('\nYou can proceed with testing, but review failed validations above.');
      return true;
    } else {
      console.log('\n❌ VALIDATION FAILED');
      console.log('💥 Critical issues found that need to be resolved');
      console.log('\nCommon solutions:');
      console.log('   • Run "npm run build" to compile TypeScript');
      console.log('   • Run "npm install" to install dependencies');
      console.log('   • Download Whisper models if missing');
      console.log('   • Check file permissions');
      return false;
    }

  } catch (error) {
    console.error('\n💥 Validation failed with error:', error);
    return false;
  }
}

// Run validation if this script is executed directly
if (require.main === module) {
  validatePersistentEngine().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('💥 Validation script failed:', error);
    process.exit(1);
  });
}

module.exports = { validatePersistentEngine };
